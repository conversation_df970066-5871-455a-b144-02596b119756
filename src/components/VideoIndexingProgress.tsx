import React, { useState, useEffect } from 'react';
import { Download, Film, Zap, Database, CheckCircle, AlertCircle, Clock, Activity } from 'lucide-react';

interface VideoIndexingProgressProps {
  videoId: number;
  onIndexingComplete?: () => void;
}

interface IndexingUpdate {
  stage: string;
  progress_percent: number;
  frames_processed: number;
  total_frames: number;
  elapsed_time: number;
  current_message: string;
  chroma_ready?: boolean;
  gemini_ready?: boolean;
}

const VideoIndexingProgress: React.FC<VideoIndexingProgressProps> = ({ 
  videoId, 
  onIndexingComplete 
}) => {
  const [indexingUpdate, setIndexingUpdate] = useState<IndexingUpdate | null>(null);
  const [isIndexing, setIsIndexing] = useState(true);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Connect to indexing progress stream
    const es = new EventSource(`/api/v1/streaming/indexing/${videoId}/progress`);
    setEventSource(es);

    es.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'keepalive') {
          return;
        }
        
        setIndexingUpdate(data);
        
        // Check if indexing is completed
        if (data.stage === 'completed') {
          setIsIndexing(false);
          if (onIndexingComplete) {
            onIndexingComplete();
          }
          es.close();
          setEventSource(null);
        } else if (data.stage === 'error') {
          setError(data.current_message || 'Indexing failed');
          setIsIndexing(false);
          es.close();
          setEventSource(null);
        }
        
      } catch (error) {
        console.error('Error parsing indexing update:', error);
      }
    };

    es.onerror = (error) => {
      console.error('EventSource error:', error);
      setError('Lost connection to indexing progress');
      setIsIndexing(false);
      es.close();
      setEventSource(null);
    };

    // Cleanup on unmount
    return () => {
      if (es) {
        es.close();
      }
    };
  }, [videoId, onIndexingComplete]);

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'downloading':
        return <Download className="w-5 h-5 animate-pulse text-blue-500" />;
      case 'extracting_frames':
        return <Film className="w-5 h-5 animate-pulse text-purple-500" />;
      case 'encoding_vectors':
        return <Zap className="w-5 h-5 animate-pulse text-yellow-500" />;
      case 'uploading_gemini':
        return <Database className="w-5 h-5 animate-pulse text-green-500" />;
      case 'storing_vectors':
        return <Database className="w-5 h-5 animate-pulse text-indigo-500" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Activity className="w-5 h-5 animate-pulse text-gray-500" />;
    }
  };

  const getStageLabel = (stage: string) => {
    switch (stage) {
      case 'downloading':
        return 'Downloading video...';
      case 'extracting_frames':
        return 'Extracting frames with FFmpeg...';
      case 'encoding_vectors':
        return 'Encoding frames with CLIP...';
      case 'uploading_gemini':
        return 'Uploading to Gemini 2.5...';
      case 'storing_vectors':
        return 'Storing vectors in Chroma...';
      case 'completed':
        return 'Indexing completed!';
      case 'error':
        return 'Indexing failed';
      default:
        return 'Processing...';
    }
  };

  const getStageDescription = (stage: string) => {
    switch (stage) {
      case 'downloading':
        return 'Downloading highest quality ≤720p version (~3-10s)';
      case 'extracting_frames':
        return 'FFmpeg sampling at 1/5 fps (~1s)';
      case 'encoding_vectors':
        return 'CLIP encoding frames on GPU (~0.3s per batch)';
      case 'uploading_gemini':
        return 'Streaming MP4 to Gemini for native search (~3s)';
      case 'storing_vectors':
        return 'Inserting vectors into Chroma DB (~0.1s)';
      case 'completed':
        return 'Video ready for instant search!';
      case 'error':
        return 'Something went wrong during indexing';
      default:
        return 'Processing video for optimal search performance';
    }
  };

  const formatTime = (seconds: number): string => {
    return `${seconds.toFixed(1)}s`;
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <div>
            <h3 className="font-medium text-red-800">Indexing Error</h3>
            <p className="text-sm text-red-600 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!isIndexing && !indexingUpdate) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-500" />
          <div>
            <h3 className="font-medium text-green-800">Video Ready</h3>
            <p className="text-sm text-green-600 mt-1">Video is indexed and ready for search</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Zap className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Fast Path Indexing</h3>
              <p className="text-sm text-gray-600">Preparing video for instant search</p>
            </div>
          </div>
          {indexingUpdate && (
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                {formatTime(indexingUpdate.elapsed_time)}
              </div>
              <div className="text-xs text-gray-500">elapsed</div>
            </div>
          )}
        </div>

        {/* Current Stage */}
        {indexingUpdate && (
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              {getStageIcon(indexingUpdate.stage)}
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium text-gray-900">
                    {getStageLabel(indexingUpdate.stage)}
                  </span>
                  <span className="text-sm text-gray-500">
                    {Math.round(indexingUpdate.progress_percent)}%
                  </span>
                </div>
                
                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(indexingUpdate.progress_percent, 100)}%` }}
                  />
                </div>
                
                <p className="text-sm text-gray-600 mt-1">
                  {getStageDescription(indexingUpdate.stage)}
                </p>
              </div>
            </div>

            {/* Frame Progress */}
            {indexingUpdate.frames_processed > 0 && (
              <div className="flex items-center justify-between text-sm text-gray-600 bg-white/50 rounded px-3 py-2">
                <span>Frames processed:</span>
                <span className="font-medium">
                  {indexingUpdate.frames_processed}/{indexingUpdate.total_frames}
                </span>
              </div>
            )}

            {/* Status Indicators */}
            <div className="grid grid-cols-2 gap-3">
              <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
                indexingUpdate.chroma_ready 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                <Database className="w-4 h-4" />
                <span className="text-sm font-medium">
                  Vector Search {indexingUpdate.chroma_ready ? 'Ready' : 'Pending'}
                </span>
              </div>
              
              <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
                indexingUpdate.gemini_ready 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                <Zap className="w-4 h-4" />
                <span className="text-sm font-medium">
                  Gemini Search {indexingUpdate.gemini_ready ? 'Ready' : 'Pending'}
                </span>
              </div>
            </div>

            {/* Current Message */}
            {indexingUpdate.current_message && (
              <div className="text-sm text-gray-700 bg-white/50 rounded px-3 py-2">
                {indexingUpdate.current_message}
              </div>
            )}
          </div>
        )}

        {/* Performance Info */}
        <div className="border-t border-blue-200 pt-4">
          <div className="text-xs text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>Expected total time:</span>
              <span className="font-medium">~6 seconds</span>
            </div>
            <div className="flex justify-between">
              <span>Search ready in:</span>
              <span className="font-medium">~6 seconds</span>
            </div>
            <div className="flex justify-between">
              <span>First query latency:</span>
              <span className="font-medium text-green-600">~20ms (from cache)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoIndexingProgress;

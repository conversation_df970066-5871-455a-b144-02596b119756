import os
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings"""

    # API Keys
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./videochat.db")

    # Application
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8081",
        "http://localhost:8082",
        "http://localhost:8083",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:8081",
        "http://127.0.0.1:8082",
        "http://127.0.0.1:8083"
    ]

    # Video Processing
    MAX_VIDEO_SIZE_MB: int = int(os.getenv("MAX_VIDEO_SIZE_MB", "500"))
    FRAME_EXTRACTION_INTERVAL: int = int(os.getenv("FRAME_EXTRACTION_INTERVAL", "5"))
    MAX_VIDEO_DURATION_SECONDS: int = int(os.getenv("MAX_VIDEO_DURATION_SECONDS", "3600"))
    MAX_FRAMES_PER_VIDEO: int = int(os.getenv("MAX_FRAMES_PER_VIDEO", "300"))
    UPLOAD_DIR: str = "./uploads"

    # Vector Database
    CHROMA_PERSIST_DIRECTORY: str = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")

    # Redis Cache
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))  # 1 hour default

    # Gemini Settings
    # Options: "gemini-2.5-flash", "gemini-2.5-pro-preview-0506", "gemini-2.0-flash-exp"
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "gemini-2.5-flash-preview-05-20")  # Latest Gemini 2.5 with video understanding
    GEMINI_TEMPERATURE: float = 0.7
    GEMINI_MAX_TOKENS: int = 2048
    
    # Video processing settings for Gemini 2.5
    GEMINI_VIDEO_RESOLUTION: str = os.getenv("GEMINI_VIDEO_RESOLUTION", "default")  # "low" for 6-hour videos
    GEMINI_MAX_VIDEO_FRAMES: int = int(os.getenv("GEMINI_MAX_VIDEO_FRAMES", "256"))  # Can go up to 7200

    # Redis Configuration
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_TTL_SECONDS: int = int(os.getenv("REDIS_TTL_SECONDS", "86400"))  # 24 hours

    # FFmpeg & Frame Processing
    FRAME_SAMPLING_INTERVAL: int = int(os.getenv("FRAME_SAMPLING_INTERVAL", "5"))  # 1 frame per 5 seconds
    FFMPEG_SCALE: str = os.getenv("FFMPEG_SCALE", "512:-1")  # Scale to 512px width
    FFMPEG_QUALITY: str = os.getenv("FFMPEG_QUALITY", "4")  # High quality

    # CLIP & Vector Search
    CLIP_MODEL: str = os.getenv("CLIP_MODEL", "ViT-B/32")
    CLIP_BATCH_SIZE: int = int(os.getenv("CLIP_BATCH_SIZE", "32"))
    ENABLE_HALF_PRECISION: bool = os.getenv("ENABLE_HALF_PRECISION", "true").lower() == "true"

    # Faiss Configuration
    FAISS_INDEX_TYPE: str = os.getenv("FAISS_INDEX_TYPE", "HNSW32")
    FAISS_SEARCH_K: int = int(os.getenv("FAISS_SEARCH_K", "200"))
    INDICES_DIR: str = os.getenv("INDICES_DIR", "indices")

    # Background Indexing
    AUTO_INDEX_UPLOADS: bool = os.getenv("AUTO_INDEX_UPLOADS", "true").lower() == "true"
    STREAM_PARTIAL_RESULTS: bool = os.getenv("STREAM_PARTIAL_RESULTS", "true").lower() == "true"

    # Memory Management
    MAX_FRAMES_IN_MEMORY: int = int(os.getenv("MAX_FRAMES_IN_MEMORY", "100"))
    CLEANUP_FRAMES_AFTER_ENCODING: bool = os.getenv("CLEANUP_FRAMES_AFTER_ENCODING", "true").lower() == "true"

    # Performance Targets
    TARGET_SEARCH_LATENCY_MS: int = int(os.getenv("TARGET_SEARCH_LATENCY_MS", "20"))
    MAX_INDEXING_TIME_SECONDS: int = int(os.getenv("MAX_INDEXING_TIME_SECONDS", "15"))

    # Legacy settings for compatibility
    MAX_CONCURRENT_GEMINI_CALLS: int = int(os.getenv("MAX_CONCURRENT_GEMINI_CALLS", "8"))
    MAX_CONCURRENT_CLIP_CALLS: int = int(os.getenv("MAX_CONCURRENT_CLIP_CALLS", "4"))
    FRAME_SAMPLING_STRIDE: int = int(os.getenv("FRAME_SAMPLING_STRIDE", "30"))
    MAX_CACHE_SIZE: int = int(os.getenv("MAX_CACHE_SIZE", "10000"))

    # Additional performance settings
    PROGRESS_UPDATE_INTERVAL_MS: int = int(os.getenv("PROGRESS_UPDATE_INTERVAL_MS", "300"))
    ENABLE_STREAMING_RESULTS: bool = os.getenv("ENABLE_STREAMING_RESULTS", "true").lower() == "true"
    CLIP_CONFIDENCE_THRESHOLD: float = float(os.getenv("CLIP_CONFIDENCE_THRESHOLD", "0.7"))
    ENABLE_COLOR_PREFILTER: bool = os.getenv("ENABLE_COLOR_PREFILTER", "true").lower() == "true"
    ENABLE_EARLY_TERMINATION: bool = os.getenv("ENABLE_EARLY_TERMINATION", "true").lower() == "true"
    CACHE_TTL_HOURS: int = int(os.getenv("CACHE_TTL_HOURS", "24"))
    ENABLE_PREFIX_CACHING: bool = os.getenv("ENABLE_PREFIX_CACHING", "true").lower() == "true"

    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields in .env file

settings = Settings()

"""
Streaming API Routes for Real-time Search Progress
Implements Server-Sent Events (SSE) for TTFT optimization
"""

import uuid
import asyncio
import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ...database.database import get_db
from ...database.models import Video
from ...services.streaming_progress import progress_manager, SearchStage
from ...services.parallel_processor import parallel_processor
from ...services.simple_video_search import SimpleVideoSearch
from ...core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/streaming", tags=["streaming"])

class StreamingSearchRequest(BaseModel):
    video_id: int
    query: str
    max_results: int = 10
    enable_early_termination: bool = True

@router.post("/search/start")
async def start_streaming_search(
    request: StreamingSearchRequest,
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Start a streaming search and return search_id for progress tracking
    """
    try:
        # Validate video exists
        video = db.query(Video).filter(Video.id == request.video_id).first()
        if not video or not video.file_path:
            raise HTTPException(status_code=404, detail="Video not found")
            
        # Generate unique search ID
        search_id = str(uuid.uuid4())
        
        # Start background search task
        asyncio.create_task(
            _execute_streaming_search(
                search_id=search_id,
                video_path=video.file_path,
                query=request.query,
                video_id=request.video_id,
                max_results=request.max_results,
                enable_early_termination=request.enable_early_termination
            )
        )
        
        return {
            "search_id": search_id,
            "status": "started",
            "message": "Search started successfully"
        }
        
    except Exception as e:
        logger.error(f"Error starting streaming search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search/{search_id}/progress")
async def stream_search_progress(search_id: str, request: Request):
    """
    Stream search progress using Server-Sent Events (SSE)
    Fixes TTFT by providing immediate feedback
    """
    
    async def event_generator():
        """Generate SSE events for search progress"""
        try:
            # Set SSE headers
            yield "event: connected\n"
            yield f"data: {{\"message\": \"Connected to search {search_id}\", \"timestamp\": \"{asyncio.get_event_loop().time()}\"}}\n\n"
            
            # Stream progress updates
            async for progress_data in progress_manager.subscribe(search_id):
                # Check if client disconnected
                if await request.is_disconnected():
                    logger.info(f"Client disconnected from search {search_id}")
                    break
                    
                yield progress_data
                
                # Add small delay to prevent overwhelming the client
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"Error in SSE stream: {e}")
            yield f"event: error\n"
            yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"
            
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@router.get("/search/{search_id}/status")
async def get_search_status(search_id: str) -> Dict[str, Any]:
    """
    Get current search status (non-streaming endpoint)
    """
    if search_id not in progress_manager.active_searches:
        raise HTTPException(status_code=404, detail="Search not found")
        
    search_data = progress_manager.active_searches[search_id]
    
    return {
        "search_id": search_id,
        "stage": search_data['stage'].value,
        "frames_processed": search_data['frames_processed'],
        "total_frames": search_data['total_frames'],
        "matches_found": search_data['matches_found'],
        "elapsed_time": asyncio.get_event_loop().time() - search_data['start_time'],
        "partial_results": search_data['partial_results'][-5:]  # Last 5 results
    }

@router.get("/stats")
async def get_processing_stats() -> Dict[str, Any]:
    """
    Get processing statistics for monitoring
    """
    return {
        "parallel_processor": parallel_processor.get_stats(),
        "active_searches": len(progress_manager.active_searches),
        "total_subscribers": sum(len(subs) for subs in progress_manager.subscribers.values())
    }

async def _execute_streaming_search(
    search_id: str,
    video_path: str,
    query: str,
    video_id: int,
    max_results: int = 10,
    enable_early_termination: bool = True
) -> None:
    """
    Execute the actual search with streaming progress updates
    """
    try:
        # Initialize search service
        search_service = SimpleVideoSearch()
        
        # Get video info for frame estimation
        import cv2
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            await progress_manager.error_search(search_id, "Could not open video file")
            return
            
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        cap.release()
        
        # Estimate frames to process (based on sampling strategy)
        estimated_frames = min(total_frames // settings.FRAME_SAMPLING_STRIDE, 50)
        
        # Start progress tracking
        await progress_manager.start_search(search_id, estimated_frames)
        
        # Stage 1: Frame extraction
        await progress_manager.update_progress(
            search_id, 
            SearchStage.EXTRACTING_FRAMES,
            message="Extracting key frames from video..."
        )
        
        # Extract frames with progress updates
        frames = await _extract_frames_with_progress(
            video_path, search_id, duration, query
        )
        
        if not frames:
            await progress_manager.error_search(search_id, "No frames could be extracted")
            return
            
        # Stage 2: Frame analysis
        await progress_manager.update_progress(
            search_id,
            SearchStage.ANALYZING_FRAMES,
            frames_processed=0,
            message=f"Analyzing {len(frames)} frames..."
        )
        
        # Process frames with streaming results
        results = await _analyze_frames_with_progress(
            frames, query, search_id, enable_early_termination
        )
        
        # Stage 3: Final processing
        await progress_manager.update_progress(
            search_id,
            SearchStage.PROCESSING_RESULTS,
            message="Processing final results..."
        )
        
        # Complete search
        await progress_manager.complete_search(search_id, results)
        
    except Exception as e:
        logger.error(f"Error in streaming search {search_id}: {e}")
        await progress_manager.error_search(search_id, str(e))

async def _extract_frames_with_progress(
    video_path: str, 
    search_id: str, 
    duration: float,
    query: str
) -> list:
    """Extract frames with progress updates"""
    
    # Use the existing frame extraction logic but with progress updates
    import cv2
    import numpy as np
    
    frames = []
    cap = cv2.VideoCapture(video_path)
    
    try:
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Smart sampling based on query type
        query_type = parallel_processor._detect_query_type(query)
        if query_type in ["person", "object"]:
            stride = settings.FRAME_SAMPLING_STRIDE // 2  # More frames for complex queries
        else:
            stride = settings.FRAME_SAMPLING_STRIDE
            
        frame_indices = list(range(0, total_frames, stride))
        
        for i, frame_idx in enumerate(frame_indices):
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                timestamp = frame_idx / fps
                
                # Basic quality check
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                quality_score = cv2.Laplacian(gray, cv2.CV_64F).var() / 1000.0  # Blur detection
                
                frames.append({
                    'frame': frame,
                    'timestamp': timestamp,
                    'frame_index': frame_idx,
                    'quality_score': min(quality_score, 1.0)
                })
                
                # Update progress every 5 frames
                if i % 5 == 0:
                    await progress_manager.update_progress(
                        search_id,
                        SearchStage.EXTRACTING_FRAMES,
                        frames_processed=i,
                        message=f"Extracted {i}/{len(frame_indices)} frames..."
                    )
                    
    finally:
        cap.release()
        
    return frames

async def _analyze_frames_with_progress(
    frames: list,
    query: str,
    search_id: str,
    enable_early_termination: bool
) -> list:
    """Analyze frames with streaming progress and partial results"""
    
    results = []
    
    async def progress_callback(processed: int, total: int):
        """Callback for frame processing progress"""
        await progress_manager.update_progress(
            search_id,
            SearchStage.ANALYZING_FRAMES,
            frames_processed=processed,
            message=f"Analyzed {processed}/{total} frames..."
        )
    
    # Use parallel processor with progress callback
    results = await parallel_processor.process_frames_parallel(
        frames=frames,
        query=query,
        processor_func=_mock_frame_processor,  # Replace with actual processor
        progress_callback=progress_callback,
        enable_early_termination=enable_early_termination
    )
    
    return results

async def _mock_frame_processor(frame_data: dict, query: str) -> dict:
    """Mock frame processor - replace with actual Gemini analysis"""
    # Simulate processing time
    await asyncio.sleep(0.1)
    
    # Mock result
    return {
        'match': len(query) % 3 == 0,  # Mock matching logic
        'confidence': 0.8,
        'description': f"Mock analysis for '{query}'"
    }

"""
CLIP + Faiss Vector Indexer
High-performance video frame indexing for sub-second search
"""

import asyncio
import logging
import os
import time
import numpy as np
from typing import List, Dict, Optional, Tuple
import pickle
from pathlib import Path

from ..core.config import settings

logger = logging.getLogger(__name__)

class CLIPIndexer:
    """CLIP-based frame indexer with Faiss storage"""
    
    def __init__(self):
        self.clip_available = False
        self.faiss_available = False
        self.model = None
        self.preprocess = None
        self.device = "cpu"
        
        # Try to import dependencies
        try:
            import clip
            import torch
            import faiss
            
            self.clip_available = True
            self.faiss_available = True
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
            logger.info(f"CLIP indexer initialized on device: {self.device}")
            
        except ImportError as e:
            logger.warning(f"CLIP/Faiss not available: {e}")
    
    async def index_video_frames(self, video_id: int, frames_data: List[Dict]) -> bool:
        """
        Index video frames using CLIP + Faiss
        
        Args:
            video_id: Video ID
            frames_data: List of frame metadata
            
        Returns:
            Success status
        """
        
        if not self.clip_available or not self.faiss_available:
            logger.warning("CLIP/Faiss not available - skipping indexing")
            return False
        
        try:
            start_time = time.time()
            
            # Load CLIP model if not loaded
            if self.model is None:
                await self._load_clip_model()
            
            # Extract embeddings
            embeddings, metadata = await self._extract_embeddings(frames_data)
            
            if len(embeddings) == 0:
                logger.warning(f"No embeddings extracted for video {video_id}")
                return False
            
            # Create and save Faiss index
            index_path = await self._create_faiss_index(video_id, embeddings, metadata)
            
            processing_time = time.time() - start_time
            logger.info(f"Indexed {len(embeddings)} frames for video {video_id} in {processing_time:.2f}s")
            logger.info(f"Index saved to: {index_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to index video {video_id}: {e}")
            return False
    
    async def _load_clip_model(self):
        """Load CLIP model"""
        
        import clip
        import torch
        
        logger.info(f"Loading CLIP model: {settings.CLIP_MODEL}")
        
        # Load model on appropriate device
        self.model, self.preprocess = clip.load(settings.CLIP_MODEL, device=self.device)
        
        # Enable half precision if supported and requested
        if settings.ENABLE_HALF_PRECISION and self.device == "cuda":
            self.model = self.model.half()
            logger.info("Enabled half precision for CLIP model")
    
    async def _extract_embeddings(self, frames_data: List[Dict]) -> Tuple[np.ndarray, List[Dict]]:
        """Extract CLIP embeddings from frames"""
        
        import torch
        from PIL import Image
        
        embeddings = []
        valid_metadata = []
        
        # Process frames in batches
        batch_size = settings.CLIP_BATCH_SIZE
        
        for i in range(0, len(frames_data), batch_size):
            batch_data = frames_data[i:i + batch_size]
            
            # Load batch of images
            images = []
            batch_metadata = []
            
            for frame_data in batch_data:
                try:
                    image = Image.open(frame_data['frame_path'])
                    processed_image = self.preprocess(image)
                    images.append(processed_image)
                    batch_metadata.append(frame_data)
                    
                except Exception as e:
                    logger.warning(f"Failed to load frame {frame_data['frame_path']}: {e}")
                    continue
            
            if not images:
                continue
            
            # Batch encode with CLIP
            try:
                image_tensor = torch.stack(images).to(self.device)
                
                # Use half precision if enabled
                if settings.ENABLE_HALF_PRECISION and self.device == "cuda":
                    image_tensor = image_tensor.half()
                
                with torch.no_grad():
                    batch_embeddings = self.model.encode_image(image_tensor)
                    
                    # Normalize embeddings
                    batch_embeddings = batch_embeddings / batch_embeddings.norm(dim=-1, keepdim=True)
                    
                    # Convert to numpy
                    batch_embeddings = batch_embeddings.cpu().float().numpy()
                
                embeddings.append(batch_embeddings)
                valid_metadata.extend(batch_metadata)
                
                logger.debug(f"Processed batch {i//batch_size + 1}/{(len(frames_data) + batch_size - 1)//batch_size}")
                
            except Exception as e:
                logger.error(f"Failed to encode batch: {e}")
                continue
            
            # Memory cleanup
            del images, image_tensor
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        if embeddings:
            all_embeddings = np.vstack(embeddings)
            logger.info(f"Extracted {all_embeddings.shape[0]} embeddings of dimension {all_embeddings.shape[1]}")
            return all_embeddings, valid_metadata
        else:
            return np.array([]), []
    
    async def _create_faiss_index(self, video_id: int, embeddings: np.ndarray, metadata: List[Dict]) -> str:
        """Create and save Faiss index"""
        
        import faiss
        
        # Ensure indices directory exists
        os.makedirs(settings.INDICES_DIR, exist_ok=True)
        
        # Create Faiss index
        dimension = embeddings.shape[1]
        
        if len(embeddings) < 100:
            # Use flat index for small datasets
            index = faiss.IndexFlatIP(dimension)  # Inner product (cosine similarity)
        else:
            # Use HNSW for larger datasets
            index = faiss.index_factory(dimension, settings.FAISS_INDEX_TYPE)
        
        # Add embeddings to index
        embeddings = embeddings.astype(np.float32)
        index.add(embeddings)
        
        # Save index
        index_path = os.path.join(settings.INDICES_DIR, f"{video_id}.faiss")
        faiss.write_index(index, index_path)
        
        # Save metadata
        metadata_path = os.path.join(settings.INDICES_DIR, f"{video_id}_metadata.pkl")
        with open(metadata_path, 'wb') as f:
            pickle.dump(metadata, f)
        
        logger.info(f"Created Faiss index with {index.ntotal} vectors")
        return index_path
    
    async def search_similar_frames(self, video_id: int, query: str, k: int = None) -> List[Dict]:
        """
        Search for similar frames using CLIP + Faiss
        
        Args:
            video_id: Video ID
            query: Text query
            k: Number of results to return
            
        Returns:
            List of similar frame results
        """
        
        if not self.clip_available or not self.faiss_available:
            return []
        
        k = k or settings.FAISS_SEARCH_K
        
        try:
            # Load index and metadata
            index, metadata = await self._load_index(video_id)
            if index is None:
                return []
            
            # Encode query text
            query_embedding = await self._encode_text(query)
            if query_embedding is None:
                return []
            
            # Search index
            scores, indices = index.search(query_embedding.reshape(1, -1), k)
            
            # Format results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(metadata):
                    result = metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    result['confidence'] = min(float(score) * 100, 100.0)  # Convert to percentage
                    results.append(result)
            
            logger.info(f"Found {len(results)} similar frames for query: '{query}'")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search frames for video {video_id}: {e}")
            return []
    
    async def _load_index(self, video_id: int) -> Tuple[Optional[object], Optional[List[Dict]]]:
        """Load Faiss index and metadata"""
        
        import faiss
        
        index_path = os.path.join(settings.INDICES_DIR, f"{video_id}.faiss")
        metadata_path = os.path.join(settings.INDICES_DIR, f"{video_id}_metadata.pkl")
        
        if not os.path.exists(index_path) or not os.path.exists(metadata_path):
            logger.warning(f"Index not found for video {video_id}")
            return None, None
        
        try:
            # Load index
            index = faiss.read_index(index_path)
            
            # Load metadata
            with open(metadata_path, 'rb') as f:
                metadata = pickle.load(f)
            
            return index, metadata
            
        except Exception as e:
            logger.error(f"Failed to load index for video {video_id}: {e}")
            return None, None
    
    async def _encode_text(self, text: str) -> Optional[np.ndarray]:
        """Encode text query using CLIP"""
        
        import clip
        import torch
        
        try:
            if self.model is None:
                await self._load_clip_model()
            
            # Tokenize and encode text
            text_tokens = clip.tokenize([text]).to(self.device)
            
            with torch.no_grad():
                text_embedding = self.model.encode_text(text_tokens)
                
                # Normalize
                text_embedding = text_embedding / text_embedding.norm(dim=-1, keepdim=True)
                
                # Convert to numpy
                text_embedding = text_embedding.cpu().float().numpy()
            
            return text_embedding[0]  # Return single embedding
            
        except Exception as e:
            logger.error(f"Failed to encode text '{text}': {e}")
            return None
    
    def is_available(self) -> bool:
        """Check if CLIP indexer is available"""
        return self.clip_available and self.faiss_available
    
    def cleanup_index(self, video_id: int):
        """Clean up index files for a video"""
        
        index_path = os.path.join(settings.INDICES_DIR, f"{video_id}.faiss")
        metadata_path = os.path.join(settings.INDICES_DIR, f"{video_id}_metadata.pkl")
        
        for path in [index_path, metadata_path]:
            if os.path.exists(path):
                try:
                    os.remove(path)
                    logger.info(f"Removed index file: {path}")
                except Exception as e:
                    logger.error(f"Failed to remove {path}: {e}")

# Global instance
clip_indexer = CLIPIndexer()

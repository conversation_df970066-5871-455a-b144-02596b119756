#!/bin/bash

# 🚀 Easy Production Deployment Script
# Choose your deployment method

echo "🚀 VideoChat AI - Production Deployment"
echo "========================================"
echo ""
echo "Choose your deployment method:"
echo "1. Railway (Recommended - Easiest)"
echo "2. Vercel + Railway (Best Performance)"
echo "3. <PERSON>er <PERSON>mpose (Local/VPS)"
echo "4. Manual Setup Guide"
echo ""

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🚂 Railway Deployment Selected"
        echo "=============================="
        echo ""
        echo "📋 Steps to deploy on Railway:"
        echo ""
        echo "1. 🌐 Go to https://railway.app"
        echo "2. 🔗 Connect your GitHub account"
        echo "3. 📁 Select this repository: $(git remote get-url origin)"
        echo "4. 🗄️  Add PostgreSQL service (Database → PostgreSQL)"
        echo "5. 🔧 Set environment variables:"
        echo "   - GEMINI_API_KEY=your_gemini_api_key_here"
        echo "   - DATABASE_URL=\${{Postgres.DATABASE_URL}}"
        echo "   - HOST=0.0.0.0"
        echo "   - PORT=\${{PORT}}"
        echo "6. 🚀 Deploy!"
        echo ""
        echo "📄 Configuration files created:"
        echo "   ✅ railway.json"
        echo "   ✅ Procfile"
        echo "   ✅ .env.railway (template)"
        echo ""
        echo "💰 Expected cost: $5-20/month"
        echo "⏱️  Deployment time: 15-30 minutes"
        ;;
    2)
        echo ""
        echo "⚡ Vercel + Railway Deployment Selected"
        echo "======================================"
        echo ""
        echo "Backend (Railway):"
        echo "1. Follow Railway steps above for backend only"
        echo ""
        echo "Frontend (Vercel):"
        echo "1. Install Vercel CLI: npm i -g vercel"
        echo "2. Deploy frontend: cd frontend && vercel --prod"
        echo "3. Set environment variable in Vercel dashboard:"
        echo "   NEXT_PUBLIC_API_URL=https://your-backend-url.railway.app"
        echo ""
        echo "💰 Expected cost: $5-15/month (frontend free on Vercel)"
        ;;
    3)
        echo ""
        echo "🐳 Docker Compose Deployment Selected"
        echo "====================================="
        echo ""
        echo "For VPS deployment:"
        echo "1. Copy docker-compose.yml to your server"
        echo "2. Set up environment variables"
        echo "3. Run: docker-compose up -d"
        echo ""
        echo "Starting Docker Compose locally..."
        if [ -f "docker-compose.yml" ]; then
            docker-compose up -d
            echo "✅ Application started on http://localhost:3000"
        else
            echo "❌ docker-compose.yml not found"
        fi
        ;;
    4)
        echo ""
        echo "📖 Manual Setup Guide"
        echo "===================="
        echo ""
        echo "📄 Check these files for detailed instructions:"
        echo "   📋 PRODUCTION_DEPLOYMENT.md - Complete deployment guide"
        echo "   🐳 docker-compose.yml - Docker configuration"
        echo "   ⚙️  backend/.env.production - Environment template"
        echo ""
        echo "🌐 Recommended providers:"
        echo "   • Railway (easiest): https://railway.app"
        echo "   • DigitalOcean: https://digitalocean.com"
        echo "   • Vercel: https://vercel.com"
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Your VideoChat AI app is production-ready!"
echo ""
echo "📚 Need help? Check:"
echo "   📋 PRODUCTION_DEPLOYMENT.md"
echo "   🐳 docker-compose.yml"
echo "   ⚙️  backend/.env.production"
echo ""
echo "🚀 Happy deploying!"

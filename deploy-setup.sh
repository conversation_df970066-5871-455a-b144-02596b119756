#!/bin/bash

# 🚀 VideoChat AI - Production Setup Script
# This script prepares your app for production deployment

echo "🚀 VideoChat AI - Production Setup"
echo "=================================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "backend" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

echo "📋 Checking project structure..."
echo "✅ Frontend files found"
echo "✅ Backend directory found"

# Check for required files
echo ""
echo "📋 Checking deployment files..."

if [ -f "railway.json" ]; then
    echo "✅ railway.json exists"
else
    echo "⚠️  Creating railway.json..."
    cat > railway.json << 'EOF'
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "cd backend && python main.py",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
EOF
    echo "✅ railway.json created"
fi

if [ -f "Procfile" ]; then
    echo "✅ Procfile exists"
else
    echo "⚠️  Creating Procfile..."
    echo "web: cd backend && python main.py" > Procfile
    echo "✅ Procfile created"
fi

if [ -f "backend/.env.example" ]; then
    echo "✅ Environment template exists"
else
    echo "⚠️  Creating environment template..."
    cat > backend/.env.example << 'EOF'
# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///./videochat.db

# Server Configuration
HOST=0.0.0.0
PORT=8002
DEBUG=false

# CORS Configuration (Important for production!)
CORS_ORIGINS=https://your-app-domain.com

# Performance Settings
MAX_VIDEO_SIZE_MB=500
FRAME_EXTRACTION_INTERVAL=5
MAX_VIDEO_DURATION_SECONDS=3600

# Optional: Redis for caching
REDIS_URL=redis://localhost:6379/0
EOF
    echo "✅ Environment template created"
fi

# Check Python dependencies
echo ""
echo "📋 Checking Python dependencies..."
if [ -f "backend/requirements.txt" ]; then
    echo "✅ requirements.txt exists"
    
    # Check for production-ready dependencies
    if grep -q "uvicorn" backend/requirements.txt; then
        echo "✅ Web server (uvicorn) included"
    else
        echo "⚠️  Adding uvicorn to requirements.txt..."
        echo "uvicorn>=0.24.0" >> backend/requirements.txt
    fi
    
    if grep -q "python-dotenv" backend/requirements.txt; then
        echo "✅ Environment loader included"
    else
        echo "⚠️  Adding python-dotenv to requirements.txt..."
        echo "python-dotenv>=1.0.0" >> backend/requirements.txt
    fi
else
    echo "❌ requirements.txt not found in backend/"
    exit 1
fi

# Check frontend build setup
echo ""
echo "📋 Checking frontend build setup..."
if [ -f "package.json" ]; then
    echo "✅ package.json exists"
    
    # Check for build script
    if grep -q '"build"' package.json; then
        echo "✅ Build script found"
    else
        echo "⚠️  Build script missing - please add to package.json"
    fi
else
    echo "❌ package.json not found"
    exit 1
fi

# Create production environment template
echo ""
echo "📋 Creating production environment template..."
cat > .env.production << 'EOF'
# 🚀 Production Environment Configuration
# Copy this file and update with your actual values

# Core Configuration
GEMINI_API_KEY=your_gemini_api_key_here
DATABASE_URL=postgresql://user:password@host:port/database
HOST=0.0.0.0
PORT=8002
DEBUG=false

# CORS Configuration (IMPORTANT!)
# Add your actual domain(s) here
CORS_ORIGINS=https://your-app-domain.com,https://your-app.railway.app

# Performance Settings
MAX_VIDEO_SIZE_MB=500
FRAME_EXTRACTION_INTERVAL=5
MAX_VIDEO_DURATION_SECONDS=3600
MAX_FRAMES_PER_VIDEO=300

# Optional: Redis for better performance
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# Optional: Advanced video search
AUTO_INDEX_UPLOADS=true
FRAME_SAMPLING_INTERVAL=5
TARGET_SEARCH_LATENCY_MS=20
EOF

echo "✅ Production environment template created (.env.production)"

# Create deployment checklist
echo ""
echo "📋 Creating deployment checklist..."
cat > DEPLOYMENT_CHECKLIST.md << 'EOF'
# 🚀 Deployment Checklist

## Before Deployment
- [ ] Get Gemini API key from [Google AI Studio](https://aistudio.google.com/)
- [ ] Push code to GitHub repository
- [ ] Test application locally
- [ ] Review environment variables

## Railway Deployment
- [ ] Sign up at [railway.app](https://railway.app)
- [ ] Connect GitHub account
- [ ] Create new project from repository
- [ ] Add PostgreSQL service
- [ ] Set environment variables:
  - [ ] GEMINI_API_KEY
  - [ ] DATABASE_URL (auto-set by Railway)
  - [ ] HOST=0.0.0.0
  - [ ] PORT (auto-set by Railway)
  - [ ] CORS_ORIGINS (your app domain)
  - [ ] DEBUG=false
- [ ] Deploy and test

## Post-Deployment
- [ ] Test video upload functionality
- [ ] Test YouTube video processing
- [ ] Test chat functionality
- [ ] Test visual search
- [ ] Set up custom domain (optional)
- [ ] Monitor application logs
- [ ] Set up error monitoring (optional)

## Security
- [ ] Verify HTTPS is enabled
- [ ] Check CORS configuration
- [ ] Ensure DEBUG=false in production
- [ ] Secure API keys

## Performance
- [ ] Test response times
- [ ] Monitor resource usage
- [ ] Set up caching if needed
- [ ] Optimize video processing settings
EOF

echo "✅ Deployment checklist created (DEPLOYMENT_CHECKLIST.md)"

echo ""
echo "🎉 Production setup complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Get your Gemini API key: https://aistudio.google.com/"
echo "2. Push to GitHub: git add . && git commit -m 'Production ready' && git push"
echo "3. Deploy on Railway: https://railway.app"
echo "4. Follow the deployment checklist in DEPLOYMENT_CHECKLIST.md"
echo ""
echo "📚 Documentation:"
echo "- Full deployment guide: PRODUCTION_DEPLOYMENT.md"
echo "- Environment template: .env.production"
echo "- Deployment checklist: DEPLOYMENT_CHECKLIST.md"
echo ""
echo "🚀 Your app is ready for production deployment!"

#!/usr/bin/env python3
"""
Test script to verify deployment dependencies
"""

def test_imports():
    """Test that all required packages can be imported"""
    try:
        print("Testing basic imports...")
        
        # Core packages
        import fastapi
        print("✅ FastAPI imported")
        
        import uvicorn
        print("✅ Uvicorn imported")
        
        # Video processing
        import cv2
        print("✅ OpenCV imported")
        
        # AI packages
        import google.generativeai as genai
        print("✅ Google Generative AI imported")
        
        # Optional packages (may not be available in lightweight deployment)
        try:
            import torch
            print("✅ PyTorch imported")
        except ImportError:
            print("⚠️ PyTorch not available (using lightweight deployment)")
        
        try:
            import clip
            print("✅ CLIP imported")
        except ImportError:
            print("⚠️ CLIP not available (using lightweight deployment)")
        
        try:
            import chromadb
            print("✅ ChromaDB imported")
        except ImportError:
            print("⚠️ ChromaDB not available")
        
        print("\n🎉 All core dependencies are working!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_ffmpeg():
    """Test FFmpeg availability"""
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg is available")
            return True
        else:
            print("❌ FFmpeg not working")
            return False
    except Exception as e:
        print(f"❌ FFmpeg error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 VideoChat AI - Deployment Test")
    print("=" * 40)
    
    imports_ok = test_imports()
    ffmpeg_ok = test_ffmpeg()
    
    if imports_ok and ffmpeg_ok:
        print("\n🚀 Deployment test PASSED - ready for production!")
    else:
        print("\n❌ Deployment test FAILED - check dependencies")

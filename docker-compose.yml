# Fast Video Search - Production Deployment
# Includes Redis, <PERSON>mpeg, PyTorch, CLIP, Faiss for sub-2s search

version: '3.8'

services:
  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: videosearch-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API with fast search capabilities
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: videosearch-backend
    ports:
      - "8002:8002"
    environment:
      # Database
      - DATABASE_URL=sqlite:///./videochat.db
      
      # Redis
      - REDIS_URL=redis://redis:6379/0
      - REDIS_TTL_SECONDS=86400
      
      # Gemini API
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      
      # Fast Search Configuration
      - AUTO_INDEX_UPLOADS=true
      - FRAME_SAMPLING_INTERVAL=5
      - CLIP_BATCH_SIZE=32
      - FAISS_SEARCH_K=200
      - TARGET_SEARCH_LATENCY_MS=20
      - MAX_INDEXING_TIME_SECONDS=15
      
      # FFmpeg Configuration
      - FFMPEG_SCALE=512:-1
      - FFMPEG_QUALITY=4
      
      # Performance Settings
      - ENABLE_HALF_PRECISION=true
      - MAX_FRAMES_IN_MEMORY=100
      - CLEANUP_FRAMES_AFTER_ENCODING=true
      
      # Shared Storage (optional)
      - SHARED_STORAGE_TYPE=local  # local, s3, weaviate, pinecone
      # - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      # - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      # - S3_BUCKET_NAME=videosearch-indices
      
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/indices:/app/indices
      - ./backend/thumbnails:/app/thumbnails
      - backend_data:/app/data
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: videosearch-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8002
    depends_on:
      - backend
    restart: unless-stopped

  # Optional: Weaviate for distributed vector storage
  # weaviate:
  #   image: semitechnologies/weaviate:1.22.4
  #   container_name: videosearch-weaviate
  #   ports:
  #     - "8080:8080"
  #   environment:
  #     QUERY_DEFAULTS_LIMIT: 25
  #     AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
  #     PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
  #     DEFAULT_VECTORIZER_MODULE: 'none'
  #     CLUSTER_HOSTNAME: 'node1'
  #   volumes:
  #     - weaviate_data:/var/lib/weaviate
  #   restart: unless-stopped

volumes:
  redis_data:
    driver: local
  backend_data:
    driver: local
  # weaviate_data:
  #   driver: local

networks:
  default:
    name: videosearch-network

[phases.setup]
nixPkgs = [
  "python311",
  "ffmpeg", 
  "git",
  "pkg-config",
  "gcc",
  "curl"
]

[phases.install]
cmds = [
  "cd backend",
  "pip install --upgrade pip",
  "pip install --no-cache-dir torch torchvision --index-url https://download.pytorch.org/whl/cpu",
  "pip install --no-cache-dir git+https://github.com/openai/CLIP.git",
  "pip install --no-cache-dir -r requirements.txt"
]

[phases.build]
cmds = []

[start]
cmd = "cd backend && python main.py"

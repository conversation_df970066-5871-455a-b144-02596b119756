[phases.setup]
nixPkgs = ["python311", "ffmpeg", "git", "pkg-config", "gcc", "curl"]

[phases.install]
cmds = [
  "pip install --upgrade pip",
  "pip install --no-cache-dir torch torchvision --index-url https://download.pytorch.org/whl/cpu",
  "pip install --no-cache-dir git+https://github.com/openai/CLIP.git",
  "pip install --no-cache-dir -r backend/requirements-railway.txt"
]

[phases.build]
cmds = []

[start]
cmd = "python backend/main.py"

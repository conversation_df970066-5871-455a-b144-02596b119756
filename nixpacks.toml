[variables]
NIXPACKS_NO_NODEJS = "1"

[phases.setup]
nixPkgs = ["python311", "ffmpeg"]
aptPkgs = ["git", "build-essential"]

[phases.install]
cmds = [
  "cd backend && pip install --upgrade pip",
  "cd backend && pip install fastapi uvicorn python-dotenv",
  "cd backend && pip install google-generativeai",
  "cd backend && pip install psycopg2-binary sqlalchemy",
  "cd backend && pip install youtube-transcript-api yt-dlp",
  "cd backend && pip install pillow opencv-python-headless"
]

[start]
cmd = "cd backend && python main.py"

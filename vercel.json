{"buildCommand": "npm run build", "outputDirectory": "dist", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "vite", "rewrites": [{"source": "/api/(.*)", "destination": "$VITE_API_URL/api/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}
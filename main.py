#!/usr/bin/env python3
"""
VideoChat AI - Production Entry Point
This file helps Railway detect this as a Python project
"""

import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Import and run the backend
if __name__ == "__main__":
    from backend.main import app
    import uvicorn
    
    # Get port from environment or default to 8002
    port = int(os.environ.get("PORT", 8002))
    host = os.environ.get("HOST", "0.0.0.0")
    
    print(f"🚀 Starting VideoChat AI on {host}:{port}")
    uvicorn.run(app, host=host, port=port)
